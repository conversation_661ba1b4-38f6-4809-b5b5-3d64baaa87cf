import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../utils/log_mapping.dart';

class MessageContentView extends StatelessWidget {
  final List<dynamic> content;
  final bool isMobile;
  final ScrollController? scrollController;

  const MessageContentView({
    super.key,
    required this.content,
    this.isMobile = false,
    this.scrollController,
  });

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp is! int) return '';
    try {
      final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return DateFormat('HH:mm:ss.SSS').format(dateTime);
    } catch (e) {
      return '';
    }
  }
  
  String _formatJson(Map<String, dynamic> json) {
    const encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(json);
  }

  @override
  Widget build(BuildContext context) {
    if (content.isEmpty) {
      return const SizedBox.shrink();
    }
    // The kotlin code skips the first element which is device info.
    // We can display it as a special header or just start from the second element.
    // Let's display all items for completeness.
    return SingleChildScrollView(
      controller: scrollController,
      child: Column(
        children: content.map((item) {
        if (item is! Map<String, dynamic>) {
          return const SizedBox.shrink();
        }

        final type = item['type']?.toString();
        String title = '未知类型';
        String subtitle = '';
        IconData icon = Icons.help_outline;
        Color iconColor = Colors.grey;
        
        final dateStr = _formatTimestamp(item['date']);

        // 默认将未知类型、未知步骤等视为一种需要关注的"错误"状态
        bool isErrorCondition = false;

        switch (type) {
          case '0': // Device Info
            title = '设备信息';
            icon = Icons.device_hub;
            iconColor = Colors.blueGrey;
            subtitle = 'App v${item['info']?['appVersion']} / Script v${item['info']?['scriptVersion']}';
            break;
          case '1': // Step
            final step = item['step']?.toString();
            final description = stepToText[step];
            title = '步骤: ${description ?? '未知步骤($step)'}';
            icon = Icons.timeline;
            iconColor = Colors.green;
            subtitle = item['args'] != null ? '参数: ${item['args']}' : '';
            if (description == null) isErrorCondition = true;
            break;
          case '2': // Command
            final cmd = item['cmd']?.toString();
            final description = cmdToText[cmd];
            title = '指令: ${description ?? '未知指令($cmd)'}';
            icon = Icons.smart_toy_outlined;
            iconColor = Colors.orange;
             subtitle = item['args'] != null ? '参数: ${item['args']}' : '';
            if (description == null) isErrorCondition = true;
            break;
          case '3': // Log
            final info = item['info']?.toString();
            final description = logToText[info] ?? '未知日志($info)';
            title = '日志: $description';
            icon = Icons.article_outlined;
            iconColor = Colors.blue;

            if (info == 'err') {
              isErrorCondition = true;
              if (item.containsKey('args') && item['args'].containsKey('code')) {
                final errCode = item['args']['code'];
                final errText = errorCodeToText[errCode] ?? '未知错误代码';
                subtitle = '错误码 $errCode: $errText';
              }
            } else {
               subtitle = item['args'] != null ? '参数: ${item['args']}' : '';
            }
            // 如果日志类型在映射中找不到，也视为错误
            if (logToText[info] == null) isErrorCondition = true;
            break;
          default:
            isErrorCondition = true;
        }

        // 如果是任何一种错误情况，统一使用红色错误图标
        if (isErrorCondition) {
          icon = Icons.error_outline;
          iconColor = Colors.red;
        }

        final tile = ExpansionTile(
          tilePadding: isMobile ? const EdgeInsets.symmetric(horizontal: 4.0, vertical: 0) : null,
          leading: Icon(icon, size: 18, color: iconColor),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12, 
                    fontWeight: FontWeight.bold,
                    color: isErrorCondition ? Colors.red : null,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
              if (dateStr.isNotEmpty) ...[
                const SizedBox(width: 8),
                Text(
                  dateStr,
                  style: const TextStyle(fontSize: 10, fontFamily: 'monospace', color: Colors.grey),
                ),
              ],
            ],
          ),
          subtitle: subtitle.isNotEmpty
              ? Text(
                  subtitle,
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                )
              : null,
          // 添加背景颜色，使错误日志更醒目
          backgroundColor: isErrorCondition ? Colors.red.withOpacity(0.05) : null,
          collapsedBackgroundColor: isErrorCondition ? Colors.red.withOpacity(0.05) : null,
          children: <Widget>[
            Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.all(16.0),
              color: Colors.grey.shade100,
              child: SelectableText(
                _formatJson(item),
                style: const TextStyle(fontFamily: 'monospace', fontSize: 10),
              ),
            ),
          ],
        );

        if (isMobile) {
          return Theme(
            data: Theme.of(context).copyWith(
              listTileTheme: const ListTileThemeData(
                dense: true,
                horizontalTitleGap: 4.0,
              ),
            ),
            child: tile,
          );
        }
        return tile;
      }).toList(),
      ),
    );
  }
} 